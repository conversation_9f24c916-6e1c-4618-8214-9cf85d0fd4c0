<!DOCTYPE html>
<html lang="id" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daftar - AccounTech</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css" rel="stylesheet" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .btn-blue {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border: none;
            transition: all 0.3s ease;
        }
        .btn-blue:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }
        .register-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <div class="min-h-screen flex">
        <!-- Left Side - Illustration -->
        <div class="hidden lg:flex lg:flex-1 register-bg items-center justify-center relative overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute top-20 left-20 w-40 h-40 bg-white rounded-full"></div>
                <div class="absolute bottom-20 right-20 w-32 h-32 bg-white rounded-full"></div>
                <div class="absolute top-1/2 right-10 w-24 h-24 bg-white rounded-full"></div>
            </div>
            
            <div class="text-center text-white relative z-10 px-12">
                <div class="text-8xl mb-8">
                    <i class="fas fa-rocket"></i>
                </div>
                <h2 class="text-4xl font-bold mb-6">Mulai Perjalanan Digital Anda</h2>
                <p class="text-xl text-blue-100 leading-relaxed max-w-md mx-auto mb-12">
                    Bergabunglah dengan 1,200+ perusahaan yang telah mempercayai AccounTech
                </p>
                
                <!-- Benefits List -->
                <div class="space-y-4 text-left max-w-sm mx-auto">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-check-circle text-green-300 text-xl"></i>
                        <span class="text-lg">Setup Gratis & Mudah</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-check-circle text-green-300 text-xl"></i>
                        <span class="text-lg">Trial 14 Hari</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-check-circle text-green-300 text-xl"></i>
                        <span class="text-lg">Support 24/7</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-check-circle text-green-300 text-xl"></i>
                        <span class="text-lg">Tanpa Kontrak</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Register Form -->
        <div class="flex-1 flex items-center justify-center px-6 lg:px-8">
            <div class="w-full max-w-lg">
                <!-- Back to Home -->
                <div class="mb-8">
                    <a href="{{ route('landing') }}" class="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Kembali ke Beranda
                    </a>
                </div>

                <!-- Logo & Title -->
                <div class="text-center mb-10">
                    <div class="text-3xl font-bold text-blue-600 mb-4">
                        <i class="fas fa-calculator mr-2"></i>
                        AccounTech
                    </div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">Buat Akun Baru</h1>
                    <p class="text-gray-600 text-lg">Mulai kelola keuangan bisnis Anda hari ini</p>
                </div>

                <!-- Register Form -->
                <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                    @if(session('error'))
                        <div class="alert alert-error mb-6 rounded-xl">
                            <i class="fas fa-exclamation-circle"></i>
                            <span>{{ session('error') }}</span>
                        </div>
                    @endif

                    <form action="{{ route('register.submit') }}" method="POST" class="space-y-6">
                        @csrf
                        
                        <!-- Full Name -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text text-lg font-medium text-gray-700">Nama Lengkap</span>
                            </label>
                            <div class="relative">
                                <input type="text" name="name" placeholder="Masukkan nama lengkap" 
                                       class="input input-bordered input-lg w-full pl-12 focus:border-blue-600 @error('name') border-red-500 @enderror" 
                                       value="{{ old('name') }}" required />
                                <i class="fas fa-user absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                            @error('name')
                                <label class="label">
                                    <span class="label-text-alt text-red-500">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Email -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text text-lg font-medium text-gray-700">Email</span>
                            </label>
                            <div class="relative">
                                <input type="email" name="email" placeholder="<EMAIL>" 
                                       class="input input-bordered input-lg w-full pl-12 focus:border-blue-600 @error('email') border-red-500 @enderror" 
                                       value="{{ old('email') }}" required />
                                <i class="fas fa-envelope absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                            @error('email')
                                <label class="label">
                                    <span class="label-text-alt text-red-500">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Company Name -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text text-lg font-medium text-gray-700">Nama Perusahaan</span>
                            </label>
                            <div class="relative">
                                <input type="text" name="company" placeholder="Nama perusahaan (opsional)"
                                       class="input input-bordered input-lg w-full pl-12 focus:border-blue-600 @error('company') border-red-500 @enderror"
                                       value="{{ old('company') }}" />
                                <i class="fas fa-building absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                            @error('company')
                                <label class="label">
                                    <span class="label-text-alt text-red-500">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Role Selection -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text text-lg font-medium text-gray-700">Posisi/Role</span>
                            </label>
                            <div class="relative">
                                <select name="role_code" class="select select-bordered select-lg w-full pl-12 focus:border-blue-600 @error('role_code') border-red-500 @enderror">
                                    <option value="">Pilih posisi Anda</option>
                                    <option value="staff" {{ old('role_code') == 'staff' ? 'selected' : '' }}>Staff - Input transaksi harian</option>
                                    <option value="admin_finance" {{ old('role_code') == 'admin_finance' ? 'selected' : '' }}>Admin Finance - Kelola data keuangan</option>
                                    <option value="auditor" {{ old('role_code') == 'auditor' ? 'selected' : '' }}>Auditor - Review dan audit data</option>
                                </select>
                                <i class="fas fa-user-tag absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                            @error('role_code')
                                <label class="label">
                                    <span class="label-text-alt text-red-500">{{ $message }}</span>
                                </label>
                            @enderror
                            <label class="label">
                                <span class="label-text-alt text-gray-500">Role dapat diubah nanti oleh admin</span>
                            </label>
                        </div>

                        <!-- Password -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text text-lg font-medium text-gray-700">Password</span>
                            </label>
                            <div class="relative">
                                <input type="password" name="password" placeholder="Minimal 8 karakter" 
                                       class="input input-bordered input-lg w-full pl-12 pr-12 focus:border-blue-600 @error('password') border-red-500 @enderror" 
                                       required />
                                <i class="fas fa-lock absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <button type="button" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600" onclick="togglePassword('password')">
                                    <i class="fas fa-eye" id="toggleIcon1"></i>
                                </button>
                            </div>
                            @error('password')
                                <label class="label">
                                    <span class="label-text-alt text-red-500">{{ $message }}</span>
                                </label>
                            @enderror
                        </div>

                        <!-- Confirm Password -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text text-lg font-medium text-gray-700">Konfirmasi Password</span>
                            </label>
                            <div class="relative">
                                <input type="password" name="password_confirmation" placeholder="Ulangi password" 
                                       class="input input-bordered input-lg w-full pl-12 pr-12 focus:border-blue-600" 
                                       required />
                                <i class="fas fa-lock absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <button type="button" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600" onclick="togglePassword('password_confirmation')">
                                    <i class="fas fa-eye" id="toggleIcon2"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Terms & Conditions -->
                        <div class="form-control">
                            <label class="label cursor-pointer justify-start">
                                <input type="checkbox" name="terms" class="checkbox checkbox-blue mr-4" required />
                                <span class="label-text text-gray-600">
                                    Saya setuju dengan 
                                    <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">Syarat & Ketentuan</a> 
                                    dan 
                                    <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">Kebijakan Privasi</a>
                                </span>
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="btn btn-blue btn-lg w-full py-4 text-xl font-semibold shadow-lg hover:shadow-xl transition-all">
                            <i class="fas fa-user-plus mr-2"></i>
                            Daftar Sekarang
                        </button>
                    </form>

                    <!-- Divider -->
                    <div class="divider my-8 text-gray-400">atau</div>

                    <!-- Social Register -->
                    <div class="space-y-3">
                        <button class="btn btn-outline btn-lg w-full border-2 hover:bg-red-50 hover:border-red-500 hover:text-red-600">
                            <i class="fab fa-google mr-3 text-xl"></i>
                            Daftar dengan Google
                        </button>
                        <button class="btn btn-outline btn-lg w-full border-2 hover:bg-blue-50 hover:border-blue-500 hover:text-blue-600">
                            <i class="fab fa-microsoft mr-3 text-xl"></i>
                            Daftar dengan Microsoft
                        </button>
                    </div>

                    <!-- Login Link -->
                    <div class="text-center mt-8 pt-6 border-t border-gray-200">
                        <p class="text-gray-600">
                            Sudah punya akun? 
                            <a href="{{ route('login') }}" class="text-blue-600 hover:text-blue-700 font-semibold">
                                Masuk di sini
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function togglePassword(fieldName) {
            const passwordInput = document.querySelector(`input[name="${fieldName}"]`);
            const toggleIcon = fieldName === 'password' ? document.getElementById('toggleIcon1') : document.getElementById('toggleIcon2');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Password strength indicator
        document.querySelector('input[name="password"]').addEventListener('input', function() {
            const password = this.value;
            const strengthIndicator = document.getElementById('passwordStrength');
            
            if (password.length < 6) {
                this.classList.remove('border-green-500', 'border-yellow-500');
                this.classList.add('border-red-500');
            } else if (password.length < 8) {
                this.classList.remove('border-red-500', 'border-green-500');
                this.classList.add('border-yellow-500');
            } else {
                this.classList.remove('border-red-500', 'border-yellow-500');
                this.classList.add('border-green-500');
            }
        });

        // Form validation feedback
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Mendaftar...';
            submitBtn.disabled = true;
        });

        // Real-time password confirmation validation
        document.querySelector('input[name="password_confirmation"]').addEventListener('input', function() {
            const password = document.querySelector('input[name="password"]').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && password !== confirmPassword) {
                this.classList.add('border-red-500');
                this.classList.remove('border-green-500');
            } else if (confirmPassword && password === confirmPassword) {
                this.classList.add('border-green-500');
                this.classList.remove('border-red-500');
            }
        });
    </script>
</body>
</html>
