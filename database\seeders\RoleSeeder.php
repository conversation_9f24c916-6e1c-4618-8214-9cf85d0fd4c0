<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'Super Admin',
                'code' => 'super_admin',
                'description' => 'Akses penuh sistem - manajemen user, konfigurasi sistem, dan semua fitur',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Admin Finance',
                'code' => 'admin_finance',
                'description' => 'Kelola semua data keuangan - akun, laporan, transaksi, dan pengaturan keuangan',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Staff',
                'code' => 'staff',
                'description' => 'Input transaksi harian, kelola data operasional, dan akses laporan dasar',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Auditor',
                'code' => 'auditor',
                'description' => 'Hanya baca laporan dan data untuk audit, tidak bisa mengubah data',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('roles')->insert($roles);
    }
}
