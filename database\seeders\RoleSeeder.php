<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'Super Admin',
                'code' => 'super_admin',
                'description' => 'Akses penuh sistem - manajemen user, konfigurasi sistem, dan semua fitur',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Admin Finance',
                'code' => 'admin_finance',
                'description' => 'Kelola semua data keuangan - akun, laporan, dan transaksi',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Accountant',
                'code' => 'accountant',
                'description' => 'Input jurnal manual dan review data transaksi',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Data Entry',
                'code' => 'data_entry',
                'description' => 'Input transaksi harian - pemasukan dan pengeluaran',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Auditor',
                'code' => 'auditor',
                'description' => 'Hanya baca laporan dan data, tidak bisa mengubah',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Owner',
                'code' => 'owner',
                'description' => 'Lihat laporan keuangan dan kinerja perusahaan',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('roles')->insert($roles);
    }
}
