import './bootstrap';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';

// Import components
import LoginForm from './components/LoginForm';
import RegisterForm from './components/RegisterForm';

// Create Material-UI theme
const theme = createTheme({
    palette: {
        primary: {
            main: '#1976d2',
        },
        secondary: {
            main: '#4caf50',
        },
    },
    typography: {
        fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    },
    components: {
        MuiButton: {
            styleOverrides: {
                root: {
                    textTransform: 'none',
                    borderRadius: 8,
                },
            },
        },
        MuiTextField: {
            styleOverrides: {
                root: {
                    '& .MuiOutlinedInput-root': {
                        borderRadius: 8,
                    },
                },
            },
        },
        MuiPaper: {
            styleOverrides: {
                root: {
                    borderRadius: 12,
                },
            },
        },
    },
});

// Mount Login Form
const loginContainer = document.getElementById('login-form');
if (loginContainer) {
    const root = createRoot(loginContainer);
    root.render(
        React.createElement(ThemeProvider, { theme },
            React.createElement(CssBaseline),
            React.createElement(LoginForm)
        )
    );
}

// Mount Register Form
const registerContainer = document.getElementById('register-form');
if (registerContainer) {
    const root = createRoot(registerContainer);
    root.render(
        React.createElement(ThemeProvider, { theme },
            React.createElement(CssBaseline),
            React.createElement(RegisterForm)
        )
    );
}

// Keep Alpine.js for other parts
import Alpine from 'alpinejs';
window.Alpine = Alpine;
Alpine.start();


