<!DOCTYPE html>
<html lang="id" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - AccounTech</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css" rel="stylesheet" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .btn-blue {
            @apply bg-blue-600 hover:bg-blue-700 text-white border-0;
        }
    </style>
</head>
<body class="min-h-screen bg-white">
    <div class="min-h-screen flex">
        <!-- Left Side - Illustration -->
        <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-50 to-indigo-100 items-center justify-center p-12">
            <div class="text-center max-w-md">
                <!-- SVG Illustration of Person with Computer -->
                <div class="mb-8">
                    <svg viewBox="0 0 400 300" class="w-80 h-60 mx-auto">
                        <!-- Person -->
                        <circle cx="200" cy="80" r="25" fill="#3B82F6" opacity="0.8"/>
                        <rect x="175" y="105" width="50" height="60" rx="25" fill="#3B82F6" opacity="0.8"/>
                        <rect x="160" y="120" width="20" height="40" rx="10" fill="#3B82F6" opacity="0.6"/>
                        <rect x="220" y="120" width="20" height="40" rx="10" fill="#3B82F6" opacity="0.6"/>

                        <!-- Computer/Laptop -->
                        <rect x="120" y="180" width="160" height="100" rx="8" fill="#1F2937"/>
                        <rect x="130" y="190" width="140" height="80" rx="4" fill="#3B82F6" opacity="0.1"/>
                        <rect x="140" y="200" width="120" height="60" rx="4" fill="#FFFFFF"/>

                        <!-- Charts on screen -->
                        <rect x="150" y="210" width="30" height="20" fill="#10B981"/>
                        <rect x="185" y="215" width="30" height="15" fill="#3B82F6"/>
                        <rect x="220" y="205" width="30" height="25" fill="#8B5CF6"/>

                        <!-- Floating elements -->
                        <circle cx="80" cy="60" r="8" fill="#10B981" opacity="0.6"/>
                        <circle cx="320" cy="100" r="6" fill="#F59E0B" opacity="0.6"/>
                        <circle cx="350" cy="200" r="10" fill="#EF4444" opacity="0.6"/>
                    </svg>
                </div>

                <h2 class="text-3xl font-bold text-gray-800 mb-4">Kelola Keuangan dengan Mudah</h2>
                <p class="text-gray-600 text-lg leading-relaxed">
                    Akses dashboard analytics dan laporan keuangan real-time untuk bisnis yang lebih efisien
                </p>
            </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="w-full lg:w-1/2 flex items-center justify-center p-8">
            <div class="w-full max-w-md">
                <!-- Back to Home -->
                <div class="mb-6">
                    <a href="<?php echo e(route('landing')); ?>" class="inline-flex items-center text-gray-600 hover:text-blue-600 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Kembali ke Beranda
                    </a>
                </div>

                <!-- Logo & Title -->
                <div class="text-center mb-8">
                    <div class="text-2xl font-bold text-blue-600 mb-3">
                        <i class="fas fa-calculator mr-2"></i>
                        AccounTech
                    </div>
                    <h1 class="text-2xl font-bold text-gray-800 mb-2">Masuk ke Akun Anda</h1>
                    <p class="text-gray-600">Selamat datang kembali!</p>
                </div>

    <div class="min-h-screen flex">
        <!-- Left Side - Illustration -->
        <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-50 to-blue-100 items-center justify-center p-12">
            <div class="text-center">
                <!-- Simple SVG Illustration of Person -->
                <div class="mb-8">
                    <svg viewBox="0 0 300 300" class="w-64 h-64 mx-auto">
                        <!-- Background Circle -->
                        <circle cx="150" cy="150" r="140" fill="#3B82F6" opacity="0.1"/>

                        <!-- Person Body -->
                        <ellipse cx="150" cy="200" rx="40" ry="60" fill="#3B82F6" opacity="0.8"/>

                        <!-- Person Head -->
                        <circle cx="150" cy="120" r="30" fill="#3B82F6" opacity="0.9"/>

                        <!-- Arms -->
                        <ellipse cx="110" cy="170" rx="15" ry="35" fill="#3B82F6" opacity="0.7"/>
                        <ellipse cx="190" cy="170" rx="15" ry="35" fill="#3B82F6" opacity="0.7"/>

                        <!-- Computer/Document in hand -->
                        <rect x="85" y="155" width="25" height="18" rx="2" fill="#FFFFFF"/>
                        <rect x="87" y="157" width="21" height="14" rx="1" fill="#3B82F6" opacity="0.3"/>

                        <!-- Floating Icons -->
                        <circle cx="80" cy="80" r="8" fill="#10B981"/>
                        <text x="80" y="85" text-anchor="middle" fill="white" font-size="10">$</text>

                        <circle cx="220" cy="90" r="8" fill="#F59E0B"/>
                        <text x="220" y="95" text-anchor="middle" fill="white" font-size="8">📊</text>

                        <circle cx="250" cy="180" r="8" fill="#EF4444"/>
                        <text x="250" y="185" text-anchor="middle" fill="white" font-size="8">📈</text>
                    </svg>
                </div>

                <h2 class="text-2xl font-bold text-gray-800 mb-4">Selamat Datang di AccounTech</h2>
                <p class="text-gray-600 leading-relaxed">
                    Platform akuntansi modern untuk mengelola keuangan bisnis Anda dengan lebih efisien
                </p>
            </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="w-full lg:w-1/2 flex items-center justify-center p-8">
            <div class="w-full max-w-sm">
                <!-- Back to Home -->
                <div class="mb-6">
                    <a href="<?php echo e(route('landing')); ?>" class="inline-flex items-center text-gray-500 hover:text-blue-600 transition-colors text-sm">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Kembali ke Beranda
                    </a>
                </div>

                <!-- Logo & Title -->
                <div class="text-center mb-8">
                    <div class="text-xl font-bold text-blue-600 mb-3">
                        <i class="fas fa-calculator mr-2"></i>
                        AccounTech
                    </div>
                    <h1 class="text-2xl font-bold text-gray-800 mb-1">Masuk</h1>
                    <p class="text-gray-500 text-sm">Masuk ke akun Anda</p>
                </div>

                <!-- Alerts -->
                <?php if(session('error')): ?>
                    <div class="alert alert-error mb-4 text-sm">
                        <i class="fas fa-exclamation-circle"></i>
                        <span><?php echo e(session('error')); ?></span>
                    </div>
                <?php endif; ?>

                <?php if(session('success')): ?>
                    <div class="alert alert-success mb-4 text-sm">
                        <i class="fas fa-check-circle"></i>
                        <span><?php echo e(session('success')); ?></span>
                    </div>
                <?php endif; ?>

                <!-- Login Form -->
                <form action="<?php echo e(route('login.submit')); ?>" method="POST" class="space-y-4">

    <script>
        function togglePassword() {
            const passwordInput = document.querySelector('input[name="password"]');
            const toggleIcon = document.getElementById('toggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Form validation feedback
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Memproses...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\acounting-v1\resources\views/auth/login.blade.php ENDPATH**/ ?>