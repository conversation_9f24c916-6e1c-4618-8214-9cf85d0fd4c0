<!DOCTYPE html>
<html lang="id" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - AccounTech</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css" rel="stylesheet" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .btn-blue {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border: none;
            transition: all 0.3s ease;
        }
        .btn-blue:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <div class="min-h-screen flex">
        <!-- Left Side - Login Form -->
        <div class="flex-1 flex items-center justify-center px-6 lg:px-8">
            <div class="w-full max-w-md">
                <!-- Back to Home -->
                <div class="mb-8">
                    <a href="<?php echo e(route('landing')); ?>" class="inline-flex items-center text-blue-600 hover:text-blue-700 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Kembali ke Beranda
                    </a>
                </div>

                <!-- Logo & Title -->
                <div class="text-center mb-10">
                    <div class="text-3xl font-bold text-blue-600 mb-4">
                        <i class="fas fa-calculator mr-2"></i>
                        AccounTech
                    </div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">Selamat Datang Kembali</h1>
                    <p class="text-gray-600 text-lg">Masuk ke akun AccounTech Anda</p>
                </div>

                <!-- Login Form -->
                <div class="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
                    <?php if(session('error')): ?>
                        <div class="alert alert-error mb-6 rounded-xl">
                            <i class="fas fa-exclamation-circle"></i>
                            <span><?php echo e(session('error')); ?></span>
                        </div>
                    <?php endif; ?>

                    <?php if(session('success')): ?>
                        <div class="alert alert-success mb-6 rounded-xl">
                            <i class="fas fa-check-circle"></i>
                            <span><?php echo e(session('success')); ?></span>
                        </div>
                    <?php endif; ?>

                    <form action="<?php echo e(route('login.submit')); ?>" method="POST" class="space-y-6">
                        <?php echo csrf_field(); ?>
                        
                        <!-- Email -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text text-lg font-medium text-gray-700">Email</span>
                            </label>
                            <div class="relative">
                                <input type="email" name="email" placeholder="<EMAIL>" 
                                       class="input input-bordered input-lg w-full pl-12 focus:border-blue-600 <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       value="<?php echo e(old('email')); ?>" required />
                                <i class="fas fa-envelope absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <label class="label">
                                    <span class="label-text-alt text-red-500"><?php echo e($message); ?></span>
                                </label>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Password -->
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text text-lg font-medium text-gray-700">Password</span>
                            </label>
                            <div class="relative">
                                <input type="password" name="password" placeholder="Masukkan password" 
                                       class="input input-bordered input-lg w-full pl-12 pr-12 focus:border-blue-600 <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> border-red-500 <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       required />
                                <i class="fas fa-lock absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <button type="button" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <label class="label">
                                    <span class="label-text-alt text-red-500"><?php echo e($message); ?></span>
                                </label>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Remember Me & Forgot Password -->
                        <div class="flex items-center justify-between">
                            <label class="label cursor-pointer">
                                <input type="checkbox" name="remember" class="checkbox checkbox-blue checkbox-sm mr-3" />
                                <span class="label-text text-gray-600">Ingat saya</span>
                            </label>
                            <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">Lupa password?</a>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="btn btn-blue btn-lg w-full py-4 text-xl font-semibold shadow-lg hover:shadow-xl transition-all">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Masuk
                        </button>
                    </form>

                    <!-- Divider -->
                    <div class="divider my-8 text-gray-400">atau</div>

                    <!-- Social Login -->
                    <div class="space-y-3">
                        <button class="btn btn-outline btn-lg w-full border-2 hover:bg-red-50 hover:border-red-500 hover:text-red-600">
                            <i class="fab fa-google mr-3 text-xl"></i>
                            Masuk dengan Google
                        </button>
                        <button class="btn btn-outline btn-lg w-full border-2 hover:bg-blue-50 hover:border-blue-500 hover:text-blue-600">
                            <i class="fab fa-microsoft mr-3 text-xl"></i>
                            Masuk dengan Microsoft
                        </button>
                    </div>

                    <!-- Register Link -->
                    <div class="text-center mt-8 pt-6 border-t border-gray-200">
                        <p class="text-gray-600">
                            Belum punya akun? 
                            <a href="<?php echo e(route('register')); ?>" class="text-blue-600 hover:text-blue-700 font-semibold">
                                Daftar sekarang
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Side - Image/Illustration -->
        <div class="hidden lg:flex lg:flex-1 login-bg items-center justify-center relative overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute top-20 left-20 w-40 h-40 bg-white rounded-full"></div>
                <div class="absolute bottom-20 right-20 w-32 h-32 bg-white rounded-full"></div>
                <div class="absolute top-1/2 left-10 w-24 h-24 bg-white rounded-full"></div>
            </div>
            
            <div class="text-center text-white relative z-10 px-12">
                <div class="text-8xl mb-8">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h2 class="text-4xl font-bold mb-6">Kelola Keuangan dengan Mudah</h2>
                <p class="text-xl text-blue-100 leading-relaxed max-w-md mx-auto">
                    Akses dashboard analytics, laporan real-time, dan fitur akuntansi lengkap dalam satu platform
                </p>
                
                <!-- Features List -->
                <div class="mt-12 space-y-4 text-left max-w-sm mx-auto">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-check-circle text-green-300 text-xl"></i>
                        <span class="text-lg">Dashboard Real-time</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-check-circle text-green-300 text-xl"></i>
                        <span class="text-lg">Laporan Otomatis</span>
                    </div>
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-check-circle text-green-300 text-xl"></i>
                        <span class="text-lg">Multi-Device Access</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function togglePassword() {
            const passwordInput = document.querySelector('input[name="password"]');
            const toggleIcon = document.getElementById('toggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Form validation feedback
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Memproses...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
<?php /**PATH C:\laragon\www\acounting-v1\resources\views/auth/login.blade.php ENDPATH**/ ?>