import React, { useState } from 'react';
import {
    Box,
    Container,
    Grid,
    Paper,
    TextField,
    Button,
    Typography,
    Link,
    Checkbox,
    FormControlLabel,
    Alert,
    IconButton,
    InputAdornment,
    Divider,
    MenuItem,
} from '@mui/material';
import {
    Visibility,
    VisibilityOff,
    Email,
    Lock,
    Person,
    Business,
    ArrowBack,
    Google,
    Microsoft,
    PersonAdd,
    Work,
} from '@mui/icons-material';

const RegisterForm = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        company: '',
        role_code: '',
        password: '',
        password_confirmation: '',
        terms: false,
    });
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);

    const roles = [
        { value: 'staff', label: 'Staff - Input transaksi harian' },
        { value: 'admin_finance', label: 'Admin Finance - Kelola data keuangan' },
        { value: 'auditor', label: 'Auditor - Review dan audit data' },
    ];

    const handleChange = (e) => {
        const { name, value, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: e.target.type === 'checkbox' ? checked : value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        
        try {
            const response = await fetch('/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                window.location.href = '/dashboard';
            } else {
                const data = await response.json();
                setErrors(data.errors || { general: 'Registrasi gagal' });
            }
        } catch (error) {
            setErrors({ general: 'Terjadi kesalahan. Silakan coba lagi.' });
        } finally {
            setLoading(false);
        }
    };

    return (
        <Box sx={{ minHeight: '100vh', display: 'flex' }}>
            {/* Left Side - Illustration */}
            <Box
                sx={{
                    display: { xs: 'none', lg: 'flex' },
                    width: '50%',
                    background: 'linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%)',
                    alignItems: 'center',
                    justifyContent: 'center',
                    p: 6,
                }}
            >
                <Box sx={{ textAlign: 'center', maxWidth: 400 }}>
                    {/* Person with Rocket Illustration */}
                    <Box sx={{ mb: 4 }}>
                        <svg width="220" height="220" viewBox="0 0 220 220" style={{ margin: '0 auto' }}>
                            {/* Background Circle */}
                            <circle cx="110" cy="110" r="100" fill="#4caf50" opacity="0.1"/>
                            
                            {/* Person */}
                            <circle cx="110" cy="80" r="22" fill="#4caf50" opacity="0.8"/>
                            <ellipse cx="110" cy="140" rx="28" ry="45" fill="#4caf50" opacity="0.8"/>
                            
                            {/* Arms */}
                            <ellipse cx="82" cy="120" rx="10" ry="30" fill="#4caf50" opacity="0.7"/>
                            <ellipse cx="138" cy="120" rx="10" ry="30" fill="#4caf50" opacity="0.7"/>
                            
                            {/* Rocket in hand */}
                            <ellipse cx="65" cy="110" rx="8" ry="15" fill="#ff9800"/>
                            <polygon points="65,95 60,105 70,105" fill="#f44336"/>
                            
                            {/* Success symbols */}
                            <circle cx="40" cy="40" r="8" fill="#4caf50"/>
                            <text x="40" y="45" textAnchor="middle" fill="white" fontSize="10">✓</text>
                            
                            <circle cx="170" cy="50" r="8" fill="#2196f3"/>
                            <text x="170" y="55" textAnchor="middle" fill="white" fontSize="8">$</text>
                            
                            <circle cx="180" cy="160" r="8" fill="#9c27b0"/>
                            <text x="180" y="165" textAnchor="middle" fill="white" fontSize="8">📊</text>
                        </svg>
                    </Box>
                    
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#4caf50', mb: 2 }}>
                        Mulai Perjalanan Anda
                    </Typography>
                    <Typography variant="h6" sx={{ color: '#666', mb: 2 }}>
                        Bergabung dengan AccounTech
                    </Typography>
                    <Typography variant="body1" sx={{ color: '#888' }}>
                        Ribuan perusahaan telah mempercayai kami untuk mengelola keuangan mereka
                    </Typography>
                    
                    {/* Benefits */}
                    <Box sx={{ mt: 4, textAlign: 'left' }}>
                        {['Setup Gratis & Mudah', 'Trial 14 Hari', 'Support 24/7', 'Tanpa Kontrak'].map((benefit, index) => (
                            <Box key={index} sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                <Box
                                    sx={{
                                        width: 20,
                                        height: 20,
                                        borderRadius: '50%',
                                        backgroundColor: '#4caf50',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        mr: 2,
                                    }}
                                >
                                    <Typography sx={{ color: 'white', fontSize: 12 }}>✓</Typography>
                                </Box>
                                <Typography variant="body2" sx={{ color: '#666' }}>
                                    {benefit}
                                </Typography>
                            </Box>
                        ))}
                    </Box>
                </Box>
            </Box>

            {/* Right Side - Register Form */}
            <Box
                sx={{
                    width: { xs: '100%', lg: '50%' },
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    p: 4,
                }}
            >
                <Box sx={{ width: '100%', maxWidth: 450 }}>
                    {/* Back Button */}
                    <Link
                        href="/"
                        sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            color: '#666',
                            textDecoration: 'none',
                            mb: 3,
                            '&:hover': { color: '#1976d2' },
                        }}
                    >
                        <ArrowBack sx={{ mr: 1, fontSize: 16 }} />
                        Kembali ke Beranda
                    </Link>

                    {/* Title */}
                    <Box sx={{ textAlign: 'center', mb: 4 }}>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#333', mb: 1 }}>
                            Daftar
                        </Typography>
                        <Typography variant="body1" sx={{ color: '#666' }}>
                            Buat akun AccounTech Anda
                        </Typography>
                    </Box>

                    {/* Error Alert */}
                    {errors.general && (
                        <Alert severity="error" sx={{ mb: 3 }}>
                            {errors.general}
                        </Alert>
                    )}

                    {/* Register Form */}
                    <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
                        <form onSubmit={handleSubmit}>
                            <Grid container spacing={3}>
                                <Grid item xs={12}>
                                    <TextField
                                        fullWidth
                                        label="Nama Lengkap"
                                        name="name"
                                        value={formData.name}
                                        onChange={handleChange}
                                        error={!!errors.name}
                                        helperText={errors.name}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <Person color="action" />
                                                </InputAdornment>
                                            ),
                                        }}
                                        required
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <TextField
                                        fullWidth
                                        label="Email"
                                        name="email"
                                        type="email"
                                        value={formData.email}
                                        onChange={handleChange}
                                        error={!!errors.email}
                                        helperText={errors.email}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <Email color="action" />
                                                </InputAdornment>
                                            ),
                                        }}
                                        required
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <TextField
                                        fullWidth
                                        label="Nama Perusahaan"
                                        name="company"
                                        value={formData.company}
                                        onChange={handleChange}
                                        error={!!errors.company}
                                        helperText={errors.company || 'Opsional'}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <Business color="action" />
                                                </InputAdornment>
                                            ),
                                        }}
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <TextField
                                        fullWidth
                                        select
                                        label="Posisi/Role"
                                        name="role_code"
                                        value={formData.role_code}
                                        onChange={handleChange}
                                        error={!!errors.role_code}
                                        helperText={errors.role_code || 'Role dapat diubah nanti oleh admin'}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <Work color="action" />
                                                </InputAdornment>
                                            ),
                                        }}
                                    >
                                        {roles.map((role) => (
                                            <MenuItem key={role.value} value={role.value}>
                                                {role.label}
                                            </MenuItem>
                                        ))}
                                    </TextField>
                                </Grid>

                                <Grid item xs={12} sm={6}>
                                    <TextField
                                        fullWidth
                                        label="Password"
                                        name="password"
                                        type={showPassword ? 'text' : 'password'}
                                        value={formData.password}
                                        onChange={handleChange}
                                        error={!!errors.password}
                                        helperText={errors.password || 'Minimal 8 karakter'}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <Lock color="action" />
                                                </InputAdornment>
                                            ),
                                            endAdornment: (
                                                <InputAdornment position="end">
                                                    <IconButton
                                                        onClick={() => setShowPassword(!showPassword)}
                                                        edge="end"
                                                    >
                                                        {showPassword ? <VisibilityOff /> : <Visibility />}
                                                    </IconButton>
                                                </InputAdornment>
                                            ),
                                        }}
                                        required
                                    />
                                </Grid>

                                <Grid item xs={12} sm={6}>
                                    <TextField
                                        fullWidth
                                        label="Konfirmasi Password"
                                        name="password_confirmation"
                                        type={showConfirmPassword ? 'text' : 'password'}
                                        value={formData.password_confirmation}
                                        onChange={handleChange}
                                        InputProps={{
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <Lock color="action" />
                                                </InputAdornment>
                                            ),
                                            endAdornment: (
                                                <InputAdornment position="end">
                                                    <IconButton
                                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                                        edge="end"
                                                    >
                                                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                                                    </IconButton>
                                                </InputAdornment>
                                            ),
                                        }}
                                        required
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                name="terms"
                                                checked={formData.terms}
                                                onChange={handleChange}
                                                color="primary"
                                                required
                                            />
                                        }
                                        label={
                                            <Typography variant="body2">
                                                Saya setuju dengan{' '}
                                                <Link href="#" sx={{ color: '#1976d2' }}>
                                                    Syarat & Ketentuan
                                                </Link>{' '}
                                                dan{' '}
                                                <Link href="#" sx={{ color: '#1976d2' }}>
                                                    Kebijakan Privasi
                                                </Link>
                                            </Typography>
                                        }
                                    />
                                </Grid>

                                <Grid item xs={12}>
                                    <Button
                                        type="submit"
                                        fullWidth
                                        variant="contained"
                                        size="large"
                                        disabled={loading}
                                        startIcon={<PersonAdd />}
                                        sx={{
                                            py: 1.5,
                                            background: 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
                                            '&:hover': {
                                                background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
                                            },
                                        }}
                                    >
                                        {loading ? 'Mendaftar...' : 'Daftar Sekarang'}
                                    </Button>
                                </Grid>
                            </Grid>

                            <Divider sx={{ my: 3 }}>atau</Divider>

                            <Button
                                fullWidth
                                variant="outlined"
                                startIcon={<Google />}
                                sx={{ mb: 2, py: 1.5 }}
                            >
                                Daftar dengan Google
                            </Button>

                            <Button
                                fullWidth
                                variant="outlined"
                                startIcon={<Microsoft />}
                                sx={{ mb: 3, py: 1.5 }}
                            >
                                Daftar dengan Microsoft
                            </Button>

                            <Box sx={{ textAlign: 'center', pt: 2, borderTop: '1px solid #eee' }}>
                                <Typography variant="body2" sx={{ color: '#666' }}>
                                    Sudah punya akun?{' '}
                                    <Link href="/login" sx={{ color: '#1976d2', textDecoration: 'none', fontWeight: 'bold' }}>
                                        Masuk di sini
                                    </Link>
                                </Typography>
                            </Box>
                        </form>
                    </Paper>
                </Box>
            </Box>
        </Box>
    );
};

export default RegisterForm;
