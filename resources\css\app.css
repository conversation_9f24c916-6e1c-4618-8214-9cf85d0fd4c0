/* Custom styles for AccounTech */
body {
    font-family: 'Figtree', ui-sans-serif, system-ui, sans-serif;
    line-height: 1.6;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

/* Custom gradient backgrounds */
.gradient-bg-1 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Custom shadows */
.shadow-custom {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-custom-lg {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Hover effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

/* Button styles */
.btn-gradient {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    background: linear-gradient(135deg, #1e3a8a 0%, #2563eb 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(30, 64, 175, 0.3);
}

/* Text gradient */
.text-gradient {
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Mobile sidebar animations */
.sidebar-slide-in {
    transform: translateX(0);
}

.sidebar-slide-out {
    transform: translateX(100%);
}

/* Responsive grid utilities */
@media (max-width: 900px) {
    .mobile-sidebar-active {
        overflow: hidden;
    }

    .grid-responsive {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }
}

@media (min-width: 901px) and (max-width: 1200px) {
    .grid-responsive {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
}

@media (min-width: 1201px) {
    .grid-responsive {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }
}

/* Enhanced mobile responsiveness */
@media (max-width: 640px) {
    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .text-responsive-xl {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .text-responsive-lg {
        font-size: 1.875rem;
        line-height: 1.3;
    }
}

@media (min-width: 641px) and (max-width: 900px) {
    .text-responsive-xl {
        font-size: 3rem;
        line-height: 1.2;
    }

    .text-responsive-lg {
        font-size: 2.25rem;
        line-height: 1.3;
    }
}

/* Smooth transitions for all interactive elements */
* {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 150ms;
}
