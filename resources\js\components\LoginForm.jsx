import React, { useState } from 'react';
import {
    Box,
    Container,
    Grid,
    Paper,
    TextField,
    Button,
    Typography,
    Link,
    Checkbox,
    FormControlLabel,
    Alert,
    IconButton,
    InputAdornment,
    Divider,
} from '@mui/material';
import {
    Visibility,
    VisibilityOff,
    Email,
    Lock,
    ArrowBack,
    Google,
    Microsoft,
    Login as LoginIcon,
} from '@mui/icons-material';

const LoginForm = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [formData, setFormData] = useState({
        email: '',
        password: '',
        remember: false,
    });
    const [errors, setErrors] = useState({});
    const [loading, setLoading] = useState(false);

    const handleChange = (e) => {
        const { name, value, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: e.target.type === 'checkbox' ? checked : value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        
        try {
            const response = await fetch('/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                },
                body: JSON.stringify(formData),
            });

            if (response.ok) {
                window.location.href = '/dashboard';
            } else {
                const data = await response.json();
                setErrors(data.errors || { general: 'Login gagal' });
            }
        } catch (error) {
            setErrors({ general: 'Terjadi kesalahan. Silakan coba lagi.' });
        } finally {
            setLoading(false);
        }
    };

    return (
        <Box sx={{ minHeight: '100vh', display: 'flex' }}>
            {/* Left Side - Illustration */}
            <Box
                sx={{
                    display: { xs: 'none', lg: 'flex' },
                    width: '50%',
                    background: 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)',
                    alignItems: 'center',
                    justifyContent: 'center',
                    p: 6,
                }}
            >
                <Box sx={{ textAlign: 'center', maxWidth: 400 }}>
                    {/* Simple Person Illustration */}
                    <Box sx={{ mb: 4 }}>
                        <svg width="200" height="200" viewBox="0 0 200 200" style={{ margin: '0 auto' }}>
                            {/* Background Circle */}
                            <circle cx="100" cy="100" r="90" fill="#1976d2" opacity="0.1"/>
                            
                            {/* Person */}
                            <circle cx="100" cy="70" r="20" fill="#1976d2" opacity="0.8"/>
                            <ellipse cx="100" cy="130" rx="25" ry="40" fill="#1976d2" opacity="0.8"/>
                            
                            {/* Arms */}
                            <ellipse cx="75" cy="110" rx="8" ry="25" fill="#1976d2" opacity="0.7"/>
                            <ellipse cx="125" cy="110" rx="8" ry="25" fill="#1976d2" opacity="0.7"/>
                            
                            {/* Computer */}
                            <rect x="60" y="100" width="20" height="15" rx="2" fill="#fff"/>
                            <rect x="62" y="102" width="16" height="11" rx="1" fill="#1976d2" opacity="0.3"/>
                            
                            {/* Floating Elements */}
                            <circle cx="50" cy="50" r="6" fill="#4caf50"/>
                            <circle cx="150" cy="60" r="5" fill="#ff9800"/>
                            <circle cx="160" cy="140" r="7" fill="#f44336"/>
                        </svg>
                    </Box>
                    
                    <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#1976d2', mb: 2 }}>
                        AccounTech
                    </Typography>
                    <Typography variant="h6" sx={{ color: '#666', mb: 2 }}>
                        Kelola Keuangan dengan Mudah
                    </Typography>
                    <Typography variant="body1" sx={{ color: '#888' }}>
                        Platform akuntansi modern untuk bisnis yang lebih efisien
                    </Typography>
                </Box>
            </Box>

            {/* Right Side - Login Form */}
            <Box
                sx={{
                    width: { xs: '100%', lg: '50%' },
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    p: 4,
                }}
            >
                <Box sx={{ width: '100%', maxWidth: 400 }}>
                    {/* Back Button */}
                    <Link
                        href="/"
                        sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            color: '#666',
                            textDecoration: 'none',
                            mb: 3,
                            '&:hover': { color: '#1976d2' },
                        }}
                    >
                        <ArrowBack sx={{ mr: 1, fontSize: 16 }} />
                        Kembali ke Beranda
                    </Link>

                    {/* Title */}
                    <Box sx={{ textAlign: 'center', mb: 4 }}>
                        <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#333', mb: 1 }}>
                            Masuk
                        </Typography>
                        <Typography variant="body1" sx={{ color: '#666' }}>
                            Selamat datang kembali!
                        </Typography>
                    </Box>

                    {/* Error Alert */}
                    {errors.general && (
                        <Alert severity="error" sx={{ mb: 3 }}>
                            {errors.general}
                        </Alert>
                    )}

                    {/* Login Form */}
                    <Paper elevation={3} sx={{ p: 4, borderRadius: 2 }}>
                        <form onSubmit={handleSubmit}>
                            <TextField
                                fullWidth
                                label="Email"
                                name="email"
                                type="email"
                                value={formData.email}
                                onChange={handleChange}
                                error={!!errors.email}
                                helperText={errors.email}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <Email color="action" />
                                        </InputAdornment>
                                    ),
                                }}
                                sx={{ mb: 3 }}
                                required
                            />

                            <TextField
                                fullWidth
                                label="Password"
                                name="password"
                                type={showPassword ? 'text' : 'password'}
                                value={formData.password}
                                onChange={handleChange}
                                error={!!errors.password}
                                helperText={errors.password}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <Lock color="action" />
                                        </InputAdornment>
                                    ),
                                    endAdornment: (
                                        <InputAdornment position="end">
                                            <IconButton
                                                onClick={() => setShowPassword(!showPassword)}
                                                edge="end"
                                            >
                                                {showPassword ? <VisibilityOff /> : <Visibility />}
                                            </IconButton>
                                        </InputAdornment>
                                    ),
                                }}
                                sx={{ mb: 2 }}
                                required
                            />

                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                                <FormControlLabel
                                    control={
                                        <Checkbox
                                            name="remember"
                                            checked={formData.remember}
                                            onChange={handleChange}
                                            color="primary"
                                        />
                                    }
                                    label="Ingat saya"
                                />
                                <Link href="#" sx={{ color: '#1976d2', textDecoration: 'none' }}>
                                    Lupa password?
                                </Link>
                            </Box>

                            <Button
                                type="submit"
                                fullWidth
                                variant="contained"
                                size="large"
                                disabled={loading}
                                startIcon={<LoginIcon />}
                                sx={{
                                    py: 1.5,
                                    mb: 3,
                                    background: 'linear-gradient(135deg, #1976d2 0%, #1565c0 100%)',
                                    '&:hover': {
                                        background: 'linear-gradient(135deg, #1565c0 0%, #0d47a1 100%)',
                                    },
                                }}
                            >
                                {loading ? 'Memproses...' : 'Masuk'}
                            </Button>

                            <Divider sx={{ my: 3 }}>atau</Divider>

                            <Button
                                fullWidth
                                variant="outlined"
                                startIcon={<Google />}
                                sx={{ mb: 2, py: 1.5 }}
                            >
                                Masuk dengan Google
                            </Button>

                            <Button
                                fullWidth
                                variant="outlined"
                                startIcon={<Microsoft />}
                                sx={{ mb: 3, py: 1.5 }}
                            >
                                Masuk dengan Microsoft
                            </Button>

                            <Box sx={{ textAlign: 'center', pt: 2, borderTop: '1px solid #eee' }}>
                                <Typography variant="body2" sx={{ color: '#666' }}>
                                    Belum punya akun?{' '}
                                    <Link href="/register" sx={{ color: '#1976d2', textDecoration: 'none', fontWeight: 'bold' }}>
                                        Daftar sekarang
                                    </Link>
                                </Typography>
                            </Box>
                        </form>
                    </Paper>
                </Box>
            </Box>
        </Box>
    );
};

export default LoginForm;
