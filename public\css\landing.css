/* Custom styles for AccounTech Landing Page */

/* Button Styles */
.btn-blue {
    @apply bg-blue-600 border-blue-600 hover:bg-blue-700 hover:border-blue-700 text-white;
}

.btn-outline.btn-blue {
    @apply text-blue-600 border-blue-600 hover:bg-blue-600 hover:text-white;
}

/* Badge Styles */
.badge-blue {
    @apply bg-blue-600 text-white border-blue-600;
}

/* Smooth Animations */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* Hero Background Animation */
.hero {
    background: linear-gradient(-45deg, #3b82f6, #1e40af, #1d4ed8, #2563eb);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Floating CTA Animation */
.fixed .btn-circle {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero p {
        font-size: 1.125rem;
    }
    
    .stats {
        flex-direction: column;
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
    background: #3b82f6;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #2563eb;
}

/* Loading Animation for Demo Video */
.demo-loading {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
