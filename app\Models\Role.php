<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
    ];

    /**
     * Get the users for the role.
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get role by code.
     */
    public static function findByCode($code)
    {
        return static::where('code', $code)->first();
    }

    /**
     * Check if this is a super admin role.
     */
    public function isSuperAdmin()
    {
        return $this->code === 'super_admin';
    }

    /**
     * Check if this is an admin role.
     */
    public function isAdmin()
    {
        return in_array($this->code, ['super_admin', 'admin_finance']);
    }

    /**
     * Get permissions for this role.
     */
    public function getPermissions()
    {
        $permissions = [
            'super_admin' => [
                'manage_users', 'manage_roles', 'manage_settings', 
                'view_all_data', 'edit_all_data', 'delete_all_data'
            ],
            'admin_finance' => [
                'manage_accounts', 'manage_transactions', 'view_reports', 
                'edit_financial_data', 'export_data'
            ],
            'accountant' => [
                'create_journals', 'edit_transactions', 'view_reports', 
                'review_data'
            ],
            'data_entry' => [
                'create_transactions', 'edit_own_transactions', 'view_basic_reports'
            ],
            'auditor' => [
                'view_all_data', 'view_reports', 'export_reports'
            ],
            'owner' => [
                'view_reports', 'view_analytics', 'view_performance'
            ],
        ];

        return $permissions[$this->code] ?? [];
    }
}
