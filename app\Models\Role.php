<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'description',
    ];

    /**
     * Get the users for the role.
     */
    public function users()
    {
        return $this->hasMany(User::class);
    }

    /**
     * Get role by code.
     */
    public static function findByCode($code)
    {
        return static::where('code', $code)->first();
    }

    /**
     * Check if this is a super admin role.
     */
    public function isSuperAdmin()
    {
        return $this->code === 'super_admin';
    }

    /**
     * Check if this is an admin role.
     */
    public function isAdmin()
    {
        return in_array($this->code, ['super_admin', 'admin_finance']);
    }

    /**
     * Get permissions for this role.
     */
    public function getPermissions()
    {
        $permissions = [
            'super_admin' => [
                'manage_users', 'manage_roles', 'manage_settings',
                'view_all_data', 'edit_all_data', 'delete_all_data',
                'manage_system', 'backup_restore'
            ],
            'admin_finance' => [
                'manage_accounts', 'manage_transactions', 'view_reports',
                'edit_financial_data', 'export_data', 'manage_categories',
                'create_journals', 'approve_transactions'
            ],
            'staff' => [
                'create_transactions', 'edit_own_transactions', 'view_basic_reports',
                'input_daily_data', 'view_dashboard', 'upload_attachments'
            ],
            'auditor' => [
                'view_all_data', 'view_reports', 'export_reports',
                'audit_transactions', 'view_audit_trail', 'generate_audit_reports'
            ],
        ];

        return $permissions[$this->code] ?? [];
    }
}
