<!DOCTYPE html>
<html lang="id" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - AccounTech</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@4.4.24/dist/full.min.css" rel="stylesheet" type="text/css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .btn-blue {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            border: none;
            transition: all 0.3s ease;
        }
        .btn-blue:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Navbar -->
    <div class="navbar bg-white shadow-lg">
        <div class="container mx-auto px-6 lg:px-8">
            <div class="navbar-start">
                <a class="btn btn-ghost text-xl font-bold text-blue-600">
                    <i class="fas fa-calculator mr-2"></i>
                    AccounTech
                </a>
            </div>
            <div class="navbar-center hidden lg:flex">
                <ul class="menu menu-horizontal px-1">
                    <li><a href="#" class="hover:text-blue-600">Dashboard</a></li>
                    <li><a href="#" class="hover:text-blue-600">Transaksi</a></li>
                    <li><a href="#" class="hover:text-blue-600">Laporan</a></li>
                    <li><a href="#" class="hover:text-blue-600">Pengaturan</a></li>
                </ul>
            </div>
            <div class="navbar-end">
                <div class="dropdown dropdown-end">
                    <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
                        <div class="w-10 rounded-full bg-blue-600 text-white flex items-center justify-center">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <ul tabindex="0" class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                        <li><a class="justify-between">Profile <span class="badge">New</span></a></li>
                        <li><a>Settings</a></li>
                        <li>
                            <form action="{{ route('logout') }}" method="POST">
                                @csrf
                                <button type="submit" class="w-full text-left">Logout</button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container mx-auto px-6 lg:px-8 max-w-7xl py-8">
        <!-- Welcome Message -->
        @if(session('success'))
            <div class="alert alert-success mb-8 rounded-xl shadow-lg">
                <i class="fas fa-check-circle text-xl"></i>
                <span class="text-lg">{{ session('success') }}</span>
            </div>
        @endif

        <!-- Header -->
        <div class="mb-12">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">
                Selamat Datang, {{ Auth::user()->name }}! 👋
            </h1>
            <p class="text-xl text-gray-600">
                Kelola keuangan bisnis Anda dengan mudah dan profesional
            </p>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div class="card bg-white shadow-xl border border-gray-100">
                <div class="card-body p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm font-medium">Total Pemasukan</p>
                            <p class="text-3xl font-bold text-green-600">Rp 45.2M</p>
                            <p class="text-sm text-green-600 mt-1">
                                <i class="fas fa-arrow-up mr-1"></i>
                                +12% dari bulan lalu
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-arrow-trend-up text-green-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card bg-white shadow-xl border border-gray-100">
                <div class="card-body p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm font-medium">Total Pengeluaran</p>
                            <p class="text-3xl font-bold text-red-600">Rp 32.8M</p>
                            <p class="text-sm text-red-600 mt-1">
                                <i class="fas fa-arrow-down mr-1"></i>
                                -5% dari bulan lalu
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-red-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-arrow-trend-down text-red-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card bg-white shadow-xl border border-gray-100">
                <div class="card-body p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm font-medium">Laba Bersih</p>
                            <p class="text-3xl font-bold text-blue-600">Rp 12.4M</p>
                            <p class="text-sm text-blue-600 mt-1">
                                <i class="fas fa-arrow-up mr-1"></i>
                                +18% dari bulan lalu
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card bg-white shadow-xl border border-gray-100">
                <div class="card-body p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm font-medium">Transaksi Hari Ini</p>
                            <p class="text-3xl font-bold text-purple-600">24</p>
                            <p class="text-sm text-purple-600 mt-1">
                                <i class="fas fa-arrow-up mr-1"></i>
                                +3 dari kemarin
                            </p>
                        </div>
                        <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-receipt text-purple-600 text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <!-- Quick Actions Card -->
            <div class="card bg-white shadow-xl border border-gray-100">
                <div class="card-body p-8">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">
                        <i class="fas fa-bolt text-yellow-500 mr-3"></i>
                        Aksi Cepat
                    </h3>
                    <div class="space-y-4">
                        <button class="btn btn-blue btn-lg w-full justify-start">
                            <i class="fas fa-plus mr-3"></i>
                            Tambah Transaksi
                        </button>
                        <button class="btn btn-outline btn-lg w-full justify-start hover:bg-green-50 hover:border-green-500 hover:text-green-600">
                            <i class="fas fa-file-invoice mr-3"></i>
                            Buat Laporan
                        </button>
                        <button class="btn btn-outline btn-lg w-full justify-start hover:bg-purple-50 hover:border-purple-500 hover:text-purple-600">
                            <i class="fas fa-chart-bar mr-3"></i>
                            Lihat Analytics
                        </button>
                    </div>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="lg:col-span-2">
                <div class="card bg-white shadow-xl border border-gray-100">
                    <div class="card-body p-8">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-2xl font-bold text-gray-800">
                                <i class="fas fa-history text-blue-500 mr-3"></i>
                                Transaksi Terbaru
                            </h3>
                            <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">Lihat Semua</a>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="table table-zebra w-full">
                                <thead>
                                    <tr class="bg-gray-50">
                                        <th class="text-gray-700 font-semibold">Tanggal</th>
                                        <th class="text-gray-700 font-semibold">Deskripsi</th>
                                        <th class="text-gray-700 font-semibold">Kategori</th>
                                        <th class="text-gray-700 font-semibold">Jumlah</th>
                                        <th class="text-gray-700 font-semibold">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="font-medium">08/08/2024</td>
                                        <td>Penjualan Produk A</td>
                                        <td><span class="badge badge-success">Pemasukan</span></td>
                                        <td class="text-green-600 font-bold">+Rp 2.500.000</td>
                                        <td><span class="badge badge-success">Selesai</span></td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium">07/08/2024</td>
                                        <td>Pembelian Bahan Baku</td>
                                        <td><span class="badge badge-error">Pengeluaran</span></td>
                                        <td class="text-red-600 font-bold">-Rp 1.200.000</td>
                                        <td><span class="badge badge-success">Selesai</span></td>
                                    </tr>
                                    <tr>
                                        <td class="font-medium">07/08/2024</td>
                                        <td>Gaji Karyawan</td>
                                        <td><span class="badge badge-error">Pengeluaran</span></td>
                                        <td class="text-red-600 font-bold">-Rp 8.500.000</td>
                                        <td><span class="badge badge-warning">Pending</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Revenue Chart -->
            <div class="card bg-white shadow-xl border border-gray-100">
                <div class="card-body p-8">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">
                        <i class="fas fa-chart-area text-green-500 mr-3"></i>
                        Grafik Pendapatan
                    </h3>
                    <div class="h-64 bg-gradient-to-br from-green-50 to-blue-50 rounded-xl flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-chart-line text-6xl text-green-600 mb-4"></i>
                            <p class="text-lg text-gray-600">Chart akan ditampilkan di sini</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Expense Chart -->
            <div class="card bg-white shadow-xl border border-gray-100">
                <div class="card-body p-8">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">
                        <i class="fas fa-chart-pie text-purple-500 mr-3"></i>
                        Distribusi Pengeluaran
                    </h3>
                    <div class="h-64 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl flex items-center justify-center">
                        <div class="text-center">
                            <i class="fas fa-chart-pie text-6xl text-purple-600 mb-4"></i>
                            <p class="text-lg text-gray-600">Pie chart akan ditampilkan di sini</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Welcome animation
        window.addEventListener('load', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
